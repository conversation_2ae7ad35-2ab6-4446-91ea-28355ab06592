# 生产线提升率分析看板

这是一个基于Python Dash框架构建的现代化、交互式生产看板网页应用，用于分析和可视化生产线提升率数据。

## 功能特性

### 📊 数据可视化
- **交互式折线图**: 展示提升率随时间的变化趋势
- **动态图表更新**: 根据用户选择实时更新图表内容
- **悬停详情**: 鼠标悬停显示具体数值和日期信息

### 🎛️ 交互控制
- **车间选择**: 下拉菜单选择不同车间（一车间、二车间、三车间、四车间）
- **线体选择**: 根据选择的车间动态更新可用线体选项
- **总览模式**: 查看整个车间所有线体的平均提升率趋势
- **单线体模式**: 查看特定生产线的详细提升率数据

### 🎨 现代化设计
- **响应式布局**: 适配不同屏幕尺寸
- **Bootstrap主题**: 专业的视觉设计
- **卡片式布局**: 清晰的信息层次
- **优雅配色**: 舒适的视觉体验

## 系统要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install dash dash_bootstrap_components pandas plotly openpyxl
```

## 安装和运行

### 1. 准备数据文件
确保 `提升率.xlsx` 文件位于应用根目录下。

### 2. 安装依赖
```bash
pip install dash dash_bootstrap_components pandas plotly openpyxl
```

### 3. 运行应用
```bash
python app.py
```

### 4. 访问应用
- **本地访问**: http://localhost:8050
- **局域网访问**: http://<运行电脑的IP地址>:8050

## 数据格式要求

Excel文件应包含以下列：
- `车间`: 车间名称（如：一车间、二车间等）
- `线体`: 生产线名称（如：裸机2线、裸机3线等）
- `类型`: 数据类型（应用只处理"提升率"类型的数据）
- 日期列: Excel日期序列号格式的列（如：45901, 45902等）

## 使用说明

### 基本操作
1. **选择车间**: 在"车间选择"下拉菜单中选择要查看的车间
2. **选择线体**: 在"线体选择"下拉菜单中选择具体线体或总览选项
3. **查看图表**: 图表会自动更新显示相应的提升率趋势

### 查看模式
- **总览模式**: 选择"总览：该车间所有线体"查看车间整体平均提升率
- **单线体模式**: 选择具体线体名称查看该线体的详细提升率趋势

### 图表交互
- **缩放**: 使用鼠标滚轮或工具栏缩放功能
- **平移**: 拖拽图表进行平移
- **悬停**: 鼠标悬停在数据点上查看详细信息
- **重置**: 双击图表重置视图

## 技术架构

### 核心技术栈
- **Dash**: Web应用框架
- **Plotly**: 交互式图表库
- **Pandas**: 数据处理和分析
- **Bootstrap**: 前端UI框架

### 文件结构
```
├── app.py              # 主应用文件
├── 提升率.xlsx         # 数据源文件
├── README.md           # 说明文档
```

### 主要组件
- **数据加载模块**: 读取和处理Excel数据
- **布局组件**: 定义页面结构和样式
- **回调函数**: 处理用户交互和图表更新

## 故障排除

### 常见问题

**Q: 应用启动失败**
A: 检查是否安装了所有必需的依赖包，确保Python版本符合要求

**Q: 数据显示异常**
A: 确认Excel文件格式正确，包含必需的列和数据类型

**Q: 局域网无法访问**
A: 检查防火墙设置，确保8050端口未被阻止

**Q: 图表不更新**
A: 刷新浏览器页面，检查控制台是否有错误信息

### 调试模式
应用默认启用调试模式，代码修改后会自动重载。如需关闭调试模式，修改 `app.py` 中的：
```python
app.run(host='0.0.0.0', port=8050, debug=False)
```

## 扩展功能

### 可能的增强
- 添加数据导出功能
- 支持更多图表类型
- 增加数据筛选选项
- 添加用户权限管理
- 集成数据库支持

## 联系信息

如有问题或建议，请联系开发团队。

---

**版本**: 1.0.0  
**最后更新**: 2025-09-12  
**开发者**: AI Assistant
