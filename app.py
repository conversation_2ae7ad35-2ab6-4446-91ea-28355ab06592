#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产线提升率分析看板
使用Dash框架构建的现代化交互式数据可视化应用

运行方式:
1. 在本地计算机上运行: python app.py
2. 局域网访问: http://<运行电脑的IP地址>:8050

作者: AI Assistant
日期: 2025-09-12
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import dash
from dash import dcc, html, Input, Output, callback
import plotly.express as px
import plotly.graph_objects as go
import dash_bootstrap_components as dbc

# ================================
# 数据加载与处理
# ================================

def load_and_process_data():
    """
    加载Excel文件并进行数据清洗和转换
    """
    try:
        # 读取Excel文件
        df = pd.read_excel('提升率.xlsx')
        
        # 填充车间和线体的NaN值（向下填充）
        df['车间'] = df['车间'].ffill()
        df['线体'] = df['线体'].ffill()
        
        # 筛选出"提升率"类型的数据
        df_rate = df[df['类型'] == '提升率'].copy()
        
        # 获取日期列（Excel序列号格式）
        date_columns = [col for col in df.columns if isinstance(col, (int, float)) and col > 40000]
        
        # 转换为长表格式
        df_long = df_rate.melt(
            id_vars=['车间', '线体'], 
            value_vars=date_columns,
            var_name='日期序列号', 
            value_name='提升率'
        )
        
        # 转换Excel日期序列号为实际日期
        df_long['日期'] = df_long['日期序列号'].apply(
            lambda x: datetime(1900, 1, 1) + timedelta(days=x - 2)
        )
        
        # 删除临时列并重新排序
        df_long = df_long[['车间', '线体', '日期', '提升率']].copy()
        
        # 按日期排序
        df_long = df_long.sort_values('日期').reset_index(drop=True)
        
        # 移除缺失值
        df_long = df_long.dropna(subset=['提升率'])
        
        return df_long
        
    except Exception as e:
        print(f"数据加载错误: {e}")
        return pd.DataFrame()

# 加载数据
data = load_and_process_data()

# ================================
# 应用初始化
# ================================

# 初始化Dash应用，使用Bootstrap主题
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
app.title = "生产线提升率分析看板"

# ================================
# 布局设计
# ================================

app.layout = dbc.Container([
    # 页面标题
    dbc.Row([
        dbc.Col([
            html.H1(
                "生产线提升率分析看板", 
                className="text-center mb-4 mt-3",
                style={
                    'color': '#2c3e50',
                    'font-weight': 'bold',
                    'text-shadow': '2px 2px 4px rgba(0,0,0,0.1)'
                }
            )
        ])
    ]),
    
    # 控制面板
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader([
                    html.H4("控制面板", className="mb-0", style={'color': '#34495e'})
                ]),
                dbc.CardBody([
                    dbc.Row([
                        # 车间选择
                        dbc.Col([
                            html.Label("车间选择:", style={'font-weight': 'bold', 'color': '#2c3e50'}),
                            dcc.Dropdown(
                                id='workshop-dropdown',
                                options=[
                                    {'label': workshop, 'value': workshop} 
                                    for workshop in sorted(data['车间'].unique())
                                ],
                                value=sorted(data['车间'].unique())[0] if len(data) > 0 else None,
                                style={'margin-top': '5px'}
                            )
                        ], md=6),
                        
                        # 线体选择
                        dbc.Col([
                            html.Label("线体选择:", style={'font-weight': 'bold', 'color': '#2c3e50'}),
                            dcc.Dropdown(
                                id='line-dropdown',
                                style={'margin-top': '5px'}
                            )
                        ], md=6)
                    ])
                ])
            ], style={'box-shadow': '0 4px 6px rgba(0, 0, 0, 0.1)'})
        ])
    ], className="mb-4"),
    
    # 图表展示区
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    dcc.Graph(
                        id='rate-chart',
                        style={'height': '500px'}
                    )
                ])
            ], style={'box-shadow': '0 4px 6px rgba(0, 0, 0, 0.1)'})
        ])
    ])
    
], fluid=True, style={'background-color': '#f0f2f5', 'min-height': '100vh', 'padding': '20px'})

# ================================
# 回调函数 - 交互逻辑
# ================================

@app.callback(
    Output('line-dropdown', 'options'),
    Output('line-dropdown', 'value'),
    Input('workshop-dropdown', 'value')
)
def update_line_dropdown(selected_workshop):
    """
    根据选择的车间更新线体下拉菜单
    """
    if not selected_workshop or len(data) == 0:
        return [], None
    
    # 获取选定车间的所有线体
    lines = sorted(data[data['车间'] == selected_workshop]['线体'].unique())
    
    # 构建选项列表，在开头添加总览选项
    options = [{'label': f'总览：{selected_workshop}所有线体', 'value': 'overview'}]
    options.extend([{'label': line, 'value': line} for line in lines])
    
    # 默认选择总览
    return options, 'overview'

@app.callback(
    Output('rate-chart', 'figure'),
    Input('workshop-dropdown', 'value'),
    Input('line-dropdown', 'value')
)
def update_chart(selected_workshop, selected_line):
    """
    根据选择的车间和线体更新图表
    """
    if not selected_workshop or not selected_line or len(data) == 0:
        # 返回空图表
        fig = go.Figure()
        fig.update_layout(
            title="请选择车间和线体",
            xaxis_title="日期",
            yaxis_title="提升率",
            template="plotly_white"
        )
        return fig
    
    if selected_line == 'overview':
        # 车间总览：计算该车间所有线体的平均提升率
        workshop_data = data[data['车间'] == selected_workshop]
        
        # 按日期分组计算平均值
        avg_data = workshop_data.groupby('日期')['提升率'].mean().reset_index()
        
        # 创建折线图
        fig = px.line(
            avg_data, 
            x='日期', 
            y='提升率',
            title=f'[{selected_workshop}] 全线体平均提升率趋势',
            markers=True
        )
        
        # 设置线条样式
        fig.update_traces(
            line=dict(color='#3498db', width=3),
            marker=dict(size=8, color='#e74c3c')
        )
        
    else:
        # 单条线体：显示具体线体的提升率趋势
        line_data = data[(data['车间'] == selected_workshop) & (data['线体'] == selected_line)]
        
        # 创建折线图
        fig = px.line(
            line_data, 
            x='日期', 
            y='提升率',
            title=f'[{selected_workshop}] - [{selected_line}] 提升率趋势',
            markers=True
        )
        
        # 设置线条样式
        fig.update_traces(
            line=dict(color='#27ae60', width=3),
            marker=dict(size=8, color='#e67e22')
        )
    
    # 统一图表样式设置
    fig.update_layout(
        template="plotly_white",
        title_font_size=18,
        title_font_color='#2c3e50',
        xaxis_title="日期",
        yaxis_title="提升率",
        xaxis_title_font_size=14,
        yaxis_title_font_size=14,
        hovermode='x unified',
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)'
    )
    
    # 设置坐标轴样式
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    
    return fig

# ================================
# 应用启动
# ================================

if __name__ == '__main__':
    print("=" * 50)
    print("生产线提升率分析看板启动中...")
    print("=" * 50)
    print("本地访问地址: http://localhost:8050")
    print("局域网访问: http://<本机IP地址>:8050")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动应用服务器
    # debug=True 启用调试模式，代码修改后自动重载
    # host='0.0.0.0' 允许局域网访问
    # port=8050 指定端口号
    app.run(host='0.0.0.0', port=8050, debug=True)
