#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理测试脚本
用于验证Excel数据读取和处理功能是否正常

运行方式: python test_data.py
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_data_loading():
    """测试数据加载功能"""
    print("=" * 50)
    print("测试数据加载功能")
    print("=" * 50)
    
    try:
        # 读取Excel文件
        df = pd.read_excel('提升率.xlsx')
        print(f"✓ Excel文件读取成功")
        print(f"  数据形状: {df.shape}")
        print(f"  列数: {len(df.columns)}")
        print(f"  行数: {len(df)}")
        
        # 检查必需的列
        required_columns = ['车间', '线体', '类型']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"✗ 缺少必需的列: {missing_columns}")
            return False
        else:
            print(f"✓ 所有必需的列都存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n" + "=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    try:
        # 读取Excel文件
        df = pd.read_excel('提升率.xlsx')
        
        # 填充车间和线体的NaN值
        df['车间'] = df['车间'].ffill()
        df['线体'] = df['线体'].ffill()
        print("✓ NaN值填充完成")
        
        # 筛选出"提升率"类型的数据
        df_rate = df[df['类型'] == '提升率'].copy()
        print(f"✓ 提升率数据筛选完成，共 {len(df_rate)} 行")
        
        # 获取日期列
        date_columns = [col for col in df.columns if isinstance(col, (int, float)) and col > 40000]
        print(f"✓ 识别到 {len(date_columns)} 个日期列")
        
        if len(date_columns) == 0:
            print("✗ 未找到有效的日期列")
            return False
        
        # 转换为长表格式
        df_long = df_rate.melt(
            id_vars=['车间', '线体'], 
            value_vars=date_columns,
            var_name='日期序列号', 
            value_name='提升率'
        )
        print(f"✓ 数据转换为长表格式，共 {len(df_long)} 行")
        
        # 转换日期
        df_long['日期'] = df_long['日期序列号'].apply(
            lambda x: datetime(1900, 1, 1) + timedelta(days=x - 2)
        )
        print("✓ 日期转换完成")
        
        # 移除缺失值
        df_clean = df_long.dropna(subset=['提升率'])
        print(f"✓ 清理缺失值，剩余 {len(df_clean)} 行有效数据")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理失败: {e}")
        return False

def test_data_analysis():
    """测试数据分析功能"""
    print("\n" + "=" * 50)
    print("测试数据分析功能")
    print("=" * 50)
    
    try:
        # 重新加载和处理数据
        df = pd.read_excel('提升率.xlsx')
        df['车间'] = df['车间'].ffill()
        df['线体'] = df['线体'].ffill()
        df_rate = df[df['类型'] == '提升率'].copy()
        date_columns = [col for col in df.columns if isinstance(col, (int, float)) and col > 40000]
        df_long = df_rate.melt(
            id_vars=['车间', '线体'], 
            value_vars=date_columns,
            var_name='日期序列号', 
            value_name='提升率'
        )
        df_long['日期'] = df_long['日期序列号'].apply(
            lambda x: datetime(1900, 1, 1) + timedelta(days=x - 2)
        )
        df_clean = df_long.dropna(subset=['提升率'])
        
        # 分析车间信息
        workshops = sorted(df_clean['车间'].unique())
        print(f"✓ 发现 {len(workshops)} 个车间: {workshops}")
        
        # 分析每个车间的线体
        for workshop in workshops:
            lines = sorted(df_clean[df_clean['车间'] == workshop]['线体'].unique())
            print(f"  {workshop}: {len(lines)} 条线体 - {lines}")
        
        # 分析日期范围
        date_range = df_clean['日期'].agg(['min', 'max'])
        print(f"✓ 数据日期范围: {date_range['min'].strftime('%Y-%m-%d')} 到 {date_range['max'].strftime('%Y-%m-%d')}")
        
        # 分析提升率统计
        rate_stats = df_clean['提升率'].describe()
        print(f"✓ 提升率统计信息:")
        print(f"  平均值: {rate_stats['mean']:.4f}")
        print(f"  最小值: {rate_stats['min']:.4f}")
        print(f"  最大值: {rate_stats['max']:.4f}")
        print(f"  标准差: {rate_stats['std']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("生产线提升率数据处理测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 运行所有测试
    tests = [
        ("数据加载", test_data_loading),
        ("数据处理", test_data_processing),
        ("数据分析", test_data_analysis)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    # 输出测试结果总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！数据处理功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查数据文件和代码。")
        return False

if __name__ == '__main__':
    main()
